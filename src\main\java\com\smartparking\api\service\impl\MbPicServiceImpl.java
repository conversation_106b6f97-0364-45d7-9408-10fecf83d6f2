package com.smartparking.api.service.impl;

import com.smartparking.api.bean.MbPic;
import com.smartparking.api.mapper.MbPicMapper;
import com.smartparking.api.service.MbPicService;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.List;


@Service(value = "mbPicService")
public class MbPicServiceImpl implements MbPicService {

    private static Logger _log = LoggerFactory.getLogger(MbPicServiceImpl.class);

    @Autowired
    MbPicMapper mbPicMapper;

    @Override
    public void saveImage(){
        _log.debug("图片处理--start");
        try{
            List<MbPic> pics = mbPicMapper.selectByStatus();
            if(pics != null && pics.size() > 0){
                _log.debug("MbPic size = " + pics.size());
                SimpleDateFormat format=new SimpleDateFormat("yyyyMMdd");
                //临时目录用来存放所有分片文件
                for (MbPic pic: pics) {
                    String name = pic.getName().split("\\.")[0];
                    String path = mbPicMapper.selectPathByName(name);
                    if(StringUtils.isNotEmpty(path)){
                        String tempFileDir = path.replace(pic.getName(), "");
                        File parentFileDir = new File(tempFileDir);
                        if (!parentFileDir.exists()) {
                            parentFileDir.mkdirs();
                        }
                    }else{
                        String timeStamp = format.format(pic.getAddtime());
                        String tempFileDir = "/data/image/LOT/" + timeStamp;
                        File parentFileDir = new File(tempFileDir);
                        if (!parentFileDir.exists()) {
                            parentFileDir.mkdirs();
                        }
                        path = tempFileDir + "/" + pic.getName();
                    }

                    try {
                        String img = "";
                        if(pic.getImg().contains("data:image/png;base64,")){
                            img = pic.getImg().replace("data:image/png;base64,","");
                        }else if(pic.getImg().contains("data:image/jpg;base64,")){
                            img = pic.getImg().replace("data:image/jpg;base64,","");
                        }else if(pic.getImg().contains("data:image/jpeg;base64,")){
                            img = pic.getImg().replace("data:image/jpeg;base64,","");
                        }else if(pic.getImg().contains("data:image/gif;base64,")){
                            img = pic.getImg().replace("data:image/gif;base64,","");
                        }
                        BASE64Decoder decoder = new BASE64Decoder();
                        byte[] bytes = decoder.decodeBuffer(img);
                        // 生成图片
                        OutputStream out = new FileOutputStream(path);
                        out.write(bytes);
                        out.flush();
                        out.close();

                        Thumbnails.of(path)
                                .scale(1f)
                                .outputQuality(0.25f)
                                .toFile(path);

                        File imgPath = new File(path);
                        if(imgPath.exists()){
                            mbPicMapper.deleteByPrimaryKey(pic.getId());
                        }
                        _log.debug("处理成功！");
                    } catch (Exception e) {
                        _log.debug("图片生成失败");
                    }
                }
                _log.debug("MbPic saveImage end;");
            }
        }catch (Exception e){
            _log.error("图片处理", e);
        }
        _log.debug("图片处理--end");
    }

}
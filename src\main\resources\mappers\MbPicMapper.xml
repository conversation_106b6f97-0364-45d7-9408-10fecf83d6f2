<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartparking.api.mapper.MbPicMapper">

  <resultMap id="BaseResultMap" type="com.smartparking.api.bean.MbPic">
    <id column="id" property="id" />
    <result column="name" property="name" />
    <result column="parkrecord_id" property="parkrecordId" />
    <result column="img" property="img" />
    <result column="status" property="status" />
  </resultMap>

  <select id="selectByStatus" resultMap="BaseResultMap">
    select id, name, parkrecord_id, img, status from mb_pic  where status = 0 limit 200;
  </select>

  <select id="selectIdsByStatus" resultType="java.lang.Integer">
    select id from mb_pic  where status = 0 limit 200;
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select id, name, parkrecord_id, img, status from mb_pic  where id  = #{id}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mb_pic  where id  = #{id}
  </delete>

  <select id="selectPathByName" resultType="java.lang.String" parameterType="java.lang.String">
    select storage_url1 from sp_parkinggate_uplog where quality = #{name} LIMIT 1;
  </select>

</mapper>
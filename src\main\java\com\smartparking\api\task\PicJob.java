package com.smartparking.api.task;


import com.smartparking.api.service.MbPicService;
import com.smartparking.api.utils.ApplicationContextHelper;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class Pic<PERSON>ob implements Job {

    private static final Logger logger = LoggerFactory.getLogger(PicJob.class);

    @Override
    public void execute(JobExecutionContext jobExecutionContext){
        Thread current = Thread.currentThread();
        logger.debug("PicJob.execute saveImage 定时任务Quartz:"+current.getId()+ ",name:"+current.getName());
        MbPicService mbPicService = (MbPicService) ApplicationContextHelper.getBean("mbPicService");
        mbPicService.saveImage();

    }
}

package com.smartparking.api.utils;

import org.apache.commons.lang3.StringUtils;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

/**
 * 
 * <AUTHOR>
 * 
 */
public class BaseUtil {

	/**
	 * 获得计算机名称
	 * @return
	 * @throws UnknownHostException
	 */
	public static String getHostName() throws UnknownHostException{
		 InetAddress s = InetAddress.getLocalHost();
         return s.getHostName();
	}
	
	/**
	 * 将时间转换为字符串 
	 * @param date
	 * @param formatter
	 * @return
	 */
	public static String getDateToString(Date date,String formatter){
		SimpleDateFormat format = new SimpleDateFormat(formatter);
		return format.format(date);
	}
	
	/**
	* 字符串转换成日期
	* @param str
	* @param fmt
	* @return date
	*/
	public static Date strToDate(String str,String fmt) {
	   SimpleDateFormat format = new SimpleDateFormat(fmt);
	   Date date = null;
	   try {
	    date = format.parse(str);
	   } catch (ParseException e) {
	    e.printStackTrace();
	   }
	   return date;
	}
	
	/**
	 * 读取target_resource.properties文件信息
	 * @param key
	 * @return
	 */	
	public static String readValue(String key) {
		if(StringUtils.isNotBlank(key)){
			String file = BaseUtil.class.getResource("/").getPath().replaceAll("%20", " ").toString().replaceAll("file:/", "") +"target_source.properties";
			Properties props = new Properties();
			try {
				InputStream in = new BufferedInputStream(new FileInputStream(file));
				props.load(in);
			 	//String value = new String(props.getProperty(key).getBytes(),"utf-8");
				String value = props.getProperty(key);
				if(in!=null){
					in.close();
				}
			 	return value;
			} catch (Exception e) {
		         e.printStackTrace();
		         return null;
			}
		}else{
			return null;
		}
	}
    
    /**
	 * 读取properties文件信息
	 * @param key
	 * @return
	 */	
	public static String readValueByKeyAndFile(String key,String filename) {
		if(StringUtils.isNotBlank(key)){
			String file = BaseUtil.class.getResource("/").getPath().replaceAll("%20", " ").toString().replaceAll("file:/", "") +filename;
			Properties props = new Properties();
			try {
				InputStream in = new BufferedInputStream(new FileInputStream(file));
				props.load(in);
			 	//String value = new String(props.getProperty(key).getBytes(),"utf-8");
				String value = props.getProperty(key);
			 	return value;
			} catch (Exception e) {
		         e.printStackTrace();
		         return null;
			}
		}else{
			return null;
		}
	}
}

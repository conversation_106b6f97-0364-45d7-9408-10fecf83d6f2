# Tomcat
server:
  port: 8099

spring:
  main:
    allow-circular-references: true
  datasource:
    druid:
      web-stat-filter:
        enabled: false
      stat-view-servlet:
        enabled: false
    name: test
    driver-class-name: dm.jdbc.driver.DmDriver
    url: jdbc:dm://127.0.0.1:5237?schema=jujian&useUnicode=true&characterEncoding=utf8
    username: jujian
    password: Yjcsxdl@2023
    # 使用druid数据源
    type: com.alibaba.druid.pool.DruidDataSource

mybatis:
  mapper-locations: classpath*:/mappers/*Mapper.xml
  type-aliases-package: com.smartparking.api.bean

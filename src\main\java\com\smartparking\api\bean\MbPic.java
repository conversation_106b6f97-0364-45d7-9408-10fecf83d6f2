package com.smartparking.api.bean;

import java.io.Serializable;
import java.util.Date;

public class MbPic implements Serializable {
    private Integer id;
    private String name;
    private String parkrecordId;
    private String img;
    private Integer status;
    private Date addtime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParkrecordId() {
        return parkrecordId;
    }

    public void setParkrecordId(String parkrecordId) {
        this.parkrecordId = parkrecordId;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }
}